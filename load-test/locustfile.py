import grpc
import time
import random
from locust import HttpUser, task, between, events
from trading_pb2 import OrderRequest
from trading_pb2_grpc import TradingServiceStub
from google.protobuf.json_format import MessageToDict

class GrpcUser(HttpUser):
    # Thời gian chờ giữa các task
    wait_time = between(1, 5)

    def on_start(self):
        # Kết nối gRPC đến Envoy proxy
        self.channel = grpc.insecure_channel('localhost:8080')
        self.stub = TradingServiceStub(self.channel)

    def on_stop(self):
        # Đóng channel khi user dừng
        self.channel.close()

    @task
    def place_order(self):
        method_name = 'PlaceOrder'
        start_time = time.time()
        try:
            # Tạo request ngẫu nhiên
            request = OrderRequest(
                order_id=f"order-{random.randint(1, 1000)}",
                symbol="AAPL",
                quantity=random.randint(1, 100),
                price=random.uniform(100.0, 200.0)
            )
            
            # Gọi gRPC method
            response = self.stub.PlaceOrder(request)
            response_time = (time.time() - start_time) * 1000  # <PERSON><PERSON><PERSON><PERSON> sang ms
            
            # Ghi lại request thành công vào Locust
            events.request.fire(
                request_type="grpc",
                name=method_name,
                response_time=response_time,
                response_length=0,  # Không tính kích thước response
                exception=None,
                context=MessageToDict(response)
            )
            
            # Log response (tùy chọn)
            print(f"Response: {MessageToDict(response)}")
            
        except grpc.RpcError as e:
            response_time = (time.time() - start_time) * 1000
            # Ghi lại request thất bại
            events.request.fire(
                request_type="grpc",
                name=method_name,
                response_time=response_time,
                response_length=0,
                exception=e,
                context={}
            )
            print(f"Error: {e}")