import grpc
from trading_pb2 import OrderRequest
from trading_pb2_grpc import TradingServiceStub
import time
import random

start_time = time.time()
try:
    channel = grpc.insecure_channel('localhost:8080')
    stub = TradingServiceStub(channel)
    request = OrderRequest(
        order_id=f"order-{random.randint(1, 1000)}",
        symbol="AAPL",
        quantity=random.randint(1, 100),
        price=random.uniform(100.0, 200.0)
    )
    response = stub.PlaceOrder(request)
    SampleResult.setResponseData(str(response), 'UTF-8')
    SampleResult.setSuccessful(True)
except Exception as e:
    SampleResult.setResponseData(str(e), 'UTF-8')
    SampleResult.setSuccessful(False)
SampleResult.setLatency(int((time.time() - start_time) * 1000))