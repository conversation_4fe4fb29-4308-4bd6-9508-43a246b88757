// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.12.4
// source: trading.proto

package trading

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TradingService_PlaceOrder_FullMethodName = "/trading.TradingService/PlaceOrder"
)

// TradingServiceClient is the client API for TradingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TradingServiceClient interface {
	PlaceOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error)
}

type tradingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTradingServiceClient(cc grpc.ClientConnInterface) TradingServiceClient {
	return &tradingServiceClient{cc}
}

func (c *tradingServiceClient) PlaceOrder(ctx context.Context, in *OrderRequest, opts ...grpc.CallOption) (*OrderResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(OrderResponse)
	err := c.cc.Invoke(ctx, TradingService_PlaceOrder_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TradingServiceServer is the server API for TradingService service.
// All implementations must embed UnimplementedTradingServiceServer
// for forward compatibility.
type TradingServiceServer interface {
	PlaceOrder(context.Context, *OrderRequest) (*OrderResponse, error)
	mustEmbedUnimplementedTradingServiceServer()
}

// UnimplementedTradingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTradingServiceServer struct{}

func (UnimplementedTradingServiceServer) PlaceOrder(context.Context, *OrderRequest) (*OrderResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PlaceOrder not implemented")
}
func (UnimplementedTradingServiceServer) mustEmbedUnimplementedTradingServiceServer() {}
func (UnimplementedTradingServiceServer) testEmbeddedByValue()                        {}

// UnsafeTradingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TradingServiceServer will
// result in compilation errors.
type UnsafeTradingServiceServer interface {
	mustEmbedUnimplementedTradingServiceServer()
}

func RegisterTradingServiceServer(s grpc.ServiceRegistrar, srv TradingServiceServer) {
	// If the following call pancis, it indicates UnimplementedTradingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TradingService_ServiceDesc, srv)
}

func _TradingService_PlaceOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(OrderRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TradingServiceServer).PlaceOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TradingService_PlaceOrder_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TradingServiceServer).PlaceOrder(ctx, req.(*OrderRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TradingService_ServiceDesc is the grpc.ServiceDesc for TradingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TradingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "trading.TradingService",
	HandlerType: (*TradingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PlaceOrder",
			Handler:    _TradingService_PlaceOrder_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "trading.proto",
}
