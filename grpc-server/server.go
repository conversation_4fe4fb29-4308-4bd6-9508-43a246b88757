package main

import (
	"context"
	"fmt"
	"log"
	"net"

	pb "grpc-server/protos/trading" // import path đổi thành đúng module củ<PERSON> bạn

	"google.golang.org/grpc"
)

// TradingServer implement pb.TradingServiceServer
type TradingServer struct {
	pb.UnimplementedTradingServiceServer
}

// PlaceOrder implement service
func (s *TradingServer) PlaceOrder(ctx context.Context, req *pb.OrderRequest) (*pb.OrderResponse, error) {
	log.Printf("Received Order: ID=%s, Symbol=%s, Quantity=%d, Price=%.2f",
		req.OrderId, req.Symbol, req.Quantity, req.Price)

	// Business logic xử lý order
	status := "ACCEPTED"
	if req.Quantity <= 0 {
		status = "REJECTED"
	}

	return &pb.OrderResponse{
		OrderId: req.OrderId,
		Status:  status,
	}, nil
}

func main() {
	lis, err := net.Listen("tcp", ":50051")
	if err != nil {
		log.Fatalf("failed to listen: %v", err)
	}

	grpcServer := grpc.NewServer()
	pb.RegisterTradingServiceServer(grpcServer, &TradingServer{})

	fmt.Println("🚀 gRPC server is running on port 50051...")
	if err := grpcServer.Serve(lis); err != nil {
		log.Fatalf("failed to serve: %v", err)
	}
}
